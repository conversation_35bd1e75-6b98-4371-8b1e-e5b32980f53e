# Text Video Agent API 集成开发提示词

## 概述

本文档包含将 openapi.md 中定义的 8 个 API 组合调用场景集成到便捷工具中的详细开发提示词。每个场景都遵循 promptx/tauri-desktop-app-expert 规定的开发规范。

---

## 场景1：完整图片生成流程工具

### 开发提示词

```
请开发一个完整的图片生成流程工具，集成到便捷工具页面中。

**功能要求：**
1. 提示词预审功能 - 调用 `GET /api/mj/prompt/check` 检查提示词合规性
2. 异步图片生成 - 调用 `POST /api/mj/async/generate/image` 提交生成任务
3. 状态轮询监控 - 调用 `GET /api/mj/async/query/status` 定期查询任务进度
4. 文件上传保存 - 调用 `POST /api/file/upload/s3` 将生成图片上传到云存储
5. 支持参考图片上传（可选）

**技术规范：**
- 遵循 Tauri 桌面应用开发规范
- 使用 TailwindCSS 进行样式设计
- 实现异步任务状态管理
- 提供实时进度反馈
- 错误处理和重试机制
- 支持任务取消功能

**UI/UX要求：**
- 遵循 promptx/frontend-developer 标准
- 美观的界面设计，流畅的操作体验
- 合理的信息布局和用户习惯
- 实时状态显示和进度条
- 结果预览和下载功能

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/ImageGenerationTool.tsx`
- 添加相应的 Rust 后端命令处理
- 更新工具数据配置
- 添加路由配置
```

---

## 场景2：图片到视频转换工具

### 开发提示词

```
请开发一个图片到视频转换工具，支持完整的图片到视频生成流程。

**功能要求：**
1. 参考图片上传 - 调用 `POST /api/file/upload/s3` 上传用户图片
2. 视频生成任务提交 - 调用 `POST /api/jm/async/generate/video` 提交视频生成
3. 任务状态监控 - 调用 `GET /api/jm/async/query/status` 监控生成进度
4. 模板保存功能 - 调用 `POST /api/template/create` 保存成功配置为模板
5. 支持多种视频参数配置（时长、模型类型等）

**技术规范：**
- 支持图片格式验证和预览
- 实现视频生成参数配置界面
- 异步任务队列管理
- 生成结果预览和下载
- 模板化配置保存

**UI/UX要求：**
- 拖拽上传图片界面
- 参数配置面板
- 实时进度显示
- 结果对比展示
- 模板管理界面

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/ImageToVideoTool.tsx`
- 实现图片上传组件
- 添加视频参数配置组件
- 集成模板保存功能
```

---

## 场景3：模板化批量生产工具

### 开发提示词

```
请开发一个模板化批量生产工具，支持基于模板的批量任务处理。

**功能要求：**
1. 模板列表获取 - 调用 `GET /api/template/default` 获取可用模板
2. 任务类型检查 - 调用 `GET /api/template/check/task_type` 确认可用性
3. 批量任务提交 - 调用 `POST /api/task/create/task/v2` 批量提交任务
4. 状态监控面板 - 调用 `GET /api/task/status/{task_id}` 监控所有任务
5. 支持任务队列管理和优先级设置

**技术规范：**
- 模板选择和预览功能
- 批量任务配置界面
- 并发任务控制
- 任务状态实时更新
- 失败任务重试机制

**UI/UX要求：**
- 模板卡片展示
- 批量操作界面
- 任务状态仪表板
- 进度统计图表
- 任务管理操作

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/BatchProductionTool.tsx`
- 实现模板选择组件
- 添加任务监控面板
- 集成批量操作功能
```

---

## 场景4：多模型对比生成工具

### 开发提示词

```
请开发一个多模型对比生成工具，支持同时使用多个AI模型生成内容并对比效果。

**功能要求：**
1. 模型列表获取 - 调用 `GET /api/union/img/model/list` 获取支持的模型
2. 并行任务提交 - 调用 `POST /api/union/img/sync/generate/image` 使用不同模型
3. 结果对比分析 - 提供生成结果的并排对比功能
4. 支持图片和视频模型对比
5. 性能指标统计（生成时间、质量评分等）

**技术规范：**
- 多模型并发处理
- 结果收集和整理
- 对比分析算法
- 性能指标计算
- 结果导出功能

**UI/UX要求：**
- 模型选择界面
- 并排对比展示
- 性能指标图表
- 结果评分系统
- 导出和分享功能

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/ModelComparisonTool.tsx`
- 实现模型选择组件
- 添加对比展示组件
- 集成性能分析功能
```

---

## 场景5：声音克隆与TTS工具

### 开发提示词

```
请开发一个声音克隆与TTS工具，提供完整的语音合成和声音克隆功能。

**功能要求：**
1. 音频上传 - 调用 `POST /api/302/hl_router/sync/file/upload` 上传音频素材
2. 声音克隆 - 调用 `POST /api/302/hl_router/sync/voice/clone` 创建个性化音色
3. 音色管理 - 调用 `GET /api/302/hl_router/sync/get/voices` 管理音色列表
4. 语音生成 - 调用 `POST /api/302/hl_router/sync/generate/speech` 生成语音
5. 支持多种音频参数配置（语速、音量、情感等）

**技术规范：**
- 音频文件格式验证
- 声音克隆质量评估
- 音色库管理系统
- 语音合成参数控制
- 音频播放和下载

**UI/UX要求：**
- 音频上传和预览
- 声音克隆进度显示
- 音色库管理界面
- 语音合成控制面板
- 音频播放器组件

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/VoiceCloneTool.tsx`
- 实现音频上传组件
- 添加音色管理组件
- 集成语音合成功能
```

---

## 场景6：工作流自动化工具

### 开发提示词

```
请开发一个ComfyUI工作流自动化工具，支持复杂AI处理工作流的管理和执行。

**功能要求：**
1. 节点管理 - 调用 `GET /api/comfy/fetch/running/node` 获取可用节点
2. 工作流提交 - 调用 `POST /api/comfy/async/submit/task` 提交工作流任务
3. 执行监控 - 调用 `GET /api/comfy/async/task/status` 跟踪执行状态
4. 同步执行 - 调用 `POST /api/comfy/sync/execute/workflow` 同步执行工作流
5. 工作流模板管理和可视化编辑

**技术规范：**
- 工作流JSON编辑器
- 节点状态监控
- 任务队列管理
- 工作流模板系统
- 执行日志记录

**UI/UX要求：**
- 工作流可视化编辑器
- 节点状态仪表板
- 任务执行监控
- 模板库管理
- 日志查看器

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/WorkflowAutomationTool.tsx`
- 实现工作流编辑器
- 添加节点监控组件
- 集成模板管理功能
```

---

## 场景7：图片反推与再生成工具

### 开发提示词

```
请开发一个图片反推与再生成工具，支持从图片反推提示词并重新生成优化图片。

**功能要求：**
1. 图片上传 - 调用 `POST /api/mj/sync/file/img/describe` 上传图片获取描述
2. 提示词优化 - 调用 `GET /api/mj/prompt/check` 检查和优化提示词
3. 重新生成 - 调用 `POST /api/mj/sync/image` 基于优化提示词重新生成
4. 支持批量图片处理
5. 提示词编辑和优化建议

**技术规范：**
- 图片格式支持和验证
- 提示词智能优化算法
- 生成结果对比分析
- 批量处理队列
- 结果历史记录

**UI/UX要求：**
- 图片上传和预览
- 提示词编辑器
- 生成结果对比
- 批量处理界面
- 历史记录管理

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/ImageReverseEngineeringTool.tsx`
- 实现图片分析组件
- 添加提示词编辑器
- 集成对比展示功能
```

---

## 场景8：多平台视频生成对比工具

### 开发提示词

```
请开发一个多平台视频生成对比工具，支持多个视频生成平台的并行处理和效果对比。

**功能要求：**
1. 素材准备 - 调用 `POST /api/file/upload/s3` 上传参考图片
2. 多平台并行生成：
   - 极梦平台：`POST /api/jm/sync/generate/video`
   - 302AI MJ：`POST /api/302/mj/video/sync/generate/video`
   - 302AI VEO：`POST /api/302/veo/video/sync/generate/video`
3. 效果对比分析 - 比较不同平台的生成效果和速度
4. 支持参数统一配置和个性化调整

**技术规范：**
- 多平台API统一管理
- 并行任务处理
- 结果收集和分析
- 性能指标统计
- 平台特性对比

**UI/UX要求：**
- 平台选择界面
- 参数配置面板
- 并行进度显示
- 结果对比展示
- 性能分析图表

**文件结构：**
- 创建 `apps/desktop/src/pages/tools/MultiPlatformVideoTool.tsx`
- 实现平台管理组件
- 添加对比分析组件
- 集成性能统计功能
```

---

## 通用开发规范

### 技术要求
1. 遵循 promptx/tauri-desktop-app-expert 开发规范
2. 使用 TailwindCSS 进行样式设计
3. 实现 TypeScript 类型安全
4. 添加完整的错误处理机制
5. 支持国际化（中英文）

### UI/UX标准
1. 遵循 promptx/frontend-developer 设计标准
2. 保持界面美观和操作流畅
3. 提供清晰的用户反馈
4. 实现响应式设计
5. 优化用户体验流程

### 代码结构
1. 组件化开发，提高复用性
2. 状态管理使用 React Hooks
3. API 调用封装为独立服务
4. 添加单元测试和集成测试
5. 完善的文档和注释

### 集成要求
1. 更新 `apps/desktop/src/data/tools.ts` 添加新工具
2. 在 `apps/desktop/src/App.tsx` 中添加路由
3. 更新导航菜单和工具分类
4. 添加相应的 Rust 后端命令
5. 实现数据持久化存储
